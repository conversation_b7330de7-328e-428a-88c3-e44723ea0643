2025-08-07 21:01:29 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-08-07 21:01:31 - [fm.core.services.folder_monitor_service] [INFO] - FolderMonitorService initialized
2025-08-07 21:01:31 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-07 21:01:31 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-07 21:01:31 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-07 21:01:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-08-07 21:01:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-08-07 21:01:32 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-08-07 21:01:32 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-08-07 21:01:32 - [fm.main] [INFO] - Application starting...
2025-08-07 21:01:32 - [fm.gui.styles.applier] [INFO] - Styles: BASE_FONT_SIZE=14 [source=default]
2025-08-07 21:01:32 - [fm.gui.styles.loader] [INFO] - Styles: using consolidated stylesheet: flatmate_consolidated.qss  (path=C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\gui\styles\flatmate_consolidated.qss)
2025-08-07 21:01:32 - [fm.gui.styles.loader] [INFO] - Styles: BASE_FONT_SIZE=14  FONT_SIZE replacements=1
2025-08-07 21:01:32 - [fm.gui.styles.applier] [INFO] - Styles: applied stylesheet from: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\gui\styles\flatmate_consolidated.qss
