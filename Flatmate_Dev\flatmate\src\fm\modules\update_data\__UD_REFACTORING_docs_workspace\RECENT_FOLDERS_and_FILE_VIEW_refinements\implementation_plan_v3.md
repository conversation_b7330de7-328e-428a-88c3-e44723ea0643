# Implementation Plan v3: File Info and Recent Folders - Final Verification

**Status:** Final Review
**Date:** 2025-08-07

---

## 1. Objective

This document outlines the final steps to verify and close out the implementation of the enriched file list and recent folders feature. The core logic has been successfully refactored to align with the architecture discussed in `FileListUpdated_Event_Discussion.md`. This plan focuses on final verification, minor UI refinements, and ensuring all acceptance criteria from `story_recent_folders_and_file_info_v2.md` are met.

## 2. Core Architecture Confirmation

The following components have been reviewed and their roles are confirmed to be correctly implemented:

- **`models/file_info.py`**: The `FileInfoData` dataclass is the unified data model. **(VERIFIED)**
- **`_presenter/file_info_manager.py`**: The `FileInfoManager` acts as the canonical source of truth ("The Librarian"), correctly enriching files and publishing the `FileListUpdatedEvent` with a `List[FileInfoData]`. **(VERIFIED)**
- **`_presenter/file_config_manager.py`**: Correctly handles user dialogs and manages the `recent_source_folders` list, persisting it to config. **(VERIFIED)**
- **`services/file_info_service.py`**: Used by the `FileInfoManager` as the designated service for file analysis and enrichment. **(VERIFIED)**

## 3. Verification and Refinement Plan

### Phase 1: UI Column Refinement and Data Binding

- **Task:** Update the `FileTree` widget to rename the "Status" column to "File Info" and bind it to the correct pre-formatted data property from the view model.
- **File:** `flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py`
- **Details:**
    1.  **Update Header Label:** In the `_setup_tree` method, change the column definition from `"Status"` to `"File Info"`.
        -   **Target Line:** `self._columns = ["Name", "Size", "Status", "Created"]`
        -   **Change To:** `self._columns = ["Name", "Size", "File Info", "Created"]`
    2.  **Update Data Binding:** In the `_add_file_item` method, change the logic that sets the text for this column. Instead of manually combining `bank_type` and `format_type`, use the `file_info_display` property from the `FileInfo` object (`fi`).
        -   **Target Line:** `it.setText(self._columns.index("Status"), status_text)`
        -   **Change To:** `it.setText(self._columns.index("File Info"), fi.file_info_display)`
- **Verification:** Run the application, add files, and confirm the column header is updated and displays the correct, pre-formatted string (e.g., `Kiwibank | Basic CSV` or `.CSV File`).

### Phase 2: Acceptance Criteria Checklist

This phase involves a structured manual test run to confirm every acceptance criterion from the story is met.

- **[ ] AC1: Enriched File Information in UI**
    - [ ] Select a folder with known, valid statement files.
    - [ ] **Confirm:** `Name` column shows filename.
    - [ ] **Confirm:** `Size` column shows human-readable size.
    - [ ] **Confirm:** `File Info` column shows `Bank | Format` (e.g., `Kiwibank | Basic CSV`).
    - [ ] **Confirm:** `Created` column shows date in `DD/MM/YY hh:mm am/pm` format.
    - [ ] Select a folder with an unrecognized `.csv` file.
    - [ ] **Confirm:** `File Info` column shows `.CSV File`.

- **[ ] AC2: Recent Folders Quick Access**
    - [ ] Clear recent folders from config if necessary.
    - [ ] Select a folder (`C:/test/folder_A`).
    - [ ] Close and reopen the application.
    - [ ] **Confirm:** `C:/test/folder_A` is present in the recent folders list.
    - [ ] Manually delete `folder_A` from the disk.
    - [ ] Close and reopen the application.
    - [ ] **Confirm:** `C:/test/folder_A` has been automatically removed from the list.

- **[ ] AC3: Per-Folder Monitoring**
    - [ ] Select a source folder.
    - [ ] **Confirm:** A prompt/option to monitor the folder appears.
    - [ ] Enable monitoring.
    - [ ] Copy a new, valid file into the monitored folder.
    - [ ] **Confirm:** The new file automatically appears in the UI file list, fully enriched.
    - [ ] Select a different source folder and do not enable monitoring.
    - [ ] **Confirm:** Adding files to this second folder does *not* cause them to appear in the UI.

- **[ ] AC4: Canonical Data Source**
    - [ ] Set a breakpoint in the `_on_file_list_updated` handler in the view (`ud_file_view.py`).
    - [ ] Trigger a file list change (e.g., by selecting a folder).
    - [ ] **Confirm:** The breakpoint is hit and the received event payload (`event.files`) is a `List[FileInfoData]` containing fully enriched objects, not just strings.

## 4. Definition of Done

- [ ] All verification steps in Phase 2 are completed and checked off.
- [ ] The "Status" column has been renamed to "File Info".
- [ ] The feature is considered stable, complete, and ready for production use.
- [ ] This document and the corresponding story are committed as the final record of the implementation.
