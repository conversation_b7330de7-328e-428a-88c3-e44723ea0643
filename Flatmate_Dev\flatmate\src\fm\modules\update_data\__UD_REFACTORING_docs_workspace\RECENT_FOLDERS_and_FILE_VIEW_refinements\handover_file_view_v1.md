# Handover Document: Update Data File View

**Status:** Handover
**Date:** 2025-08-07

---

## 1. Purpose

This document is to provide a clear handover for the next developer to continue work on the Update Data module's file list view. The previous work focused on establishing a solid, event-driven backend for file enrichment and recent folder management. The focus has now shifted to a significant UI/UX overhaul based on new, specific user feedback.

## 2. Current State of Core Logic (VERIFIED)

The backend logic is stable and functions as designed:

- **`FileInfoManager`**: Acts as the single source of truth for enriched file data (`List[FileInfoData]`).
- **`FileConfigManager`**: Manages user dialogs and recent folders persistence.
- **`FileInfoData` Model**: Provides a unified data object with all necessary properties for the UI.
- **Events**: The `FileListUpdatedEvent` correctly carries the enriched data to the view.

## 3. Outstanding UI/UX Requirements

The `FileTree` widget implementation does not meet user requirements. The following changes are required to replace the current tree view with a more traditional and compact file list.

### 3.1. Core Component Changes

- **[ ] Replace Tree View**: The current `QTreeWidget` is not suitable. It should be replaced with a standard `QTableWidget` or a flat `QTreeView` configured to look like a list. The goal is to eliminate the large, collapsible folder structure.
- **[ ] Compact Display**: A simple folder icon could be used in the first column next to the filename to indicate its parent, but the primary view should be a flat list.

### 3.2. Column Configuration

- **[ ] Default Columns & Order**: The visible columns, in order, must be:
    1.  `Selected Files` (This is the file's name).
    2.  `File Info`
    3.  `Size`
    4.  `Created`
- **[ ] Column Sizing**: The `Selected Files` column must be configured to stretch and take up all available horizontal space (`QHeaderView.ResizeMode.Stretch`). Other columns should resize to their contents (`ResizeToContents`).
- **[ ] Hidden Columns**: The `Type` column must be removed as a default. All non-default columns should be hidden but toggleable via a right-click context menu on the header.

### 3.3. Data Display and Formatting

- **[ ] Debug 'Created' Date**: The `Created` date is not displaying. This needs to be debugged. The data should be sourced from the system's file attributes and formatted correctly.
- **[ ] 'File Info' Formatting**: The logic for the `file_info_display` property in the `FileInfoData` model needs to be updated:
    - It must not use pipe `|` separators (e.g., `Kiwibank Basic CSV`).
    - It must abbreviate 'Co-operative Bank' to 'Co-op Bank'.

### 3.4. User Interaction

- **[ ] Selection Mode**: The table/list must be configured for multi-selection using standard keyboard modifiers.
    - `QAbstractItemView.SelectionMode.ExtendedSelection` should be set.
    - Row-level selection should be enabled (`QAbstractItemView.SelectionBehavior.SelectRows`).
- **[ ] Context Menu**: A right-click context menu on one or more selected file items must be implemented with options including:
    - **Reveal in Explorer** (or platform-equivalent).

This list constitutes the full scope of work required to meet the current user story.
